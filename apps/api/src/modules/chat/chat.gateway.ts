import { AuthService } from '@modules/auth/auth.service';
import { JwtService } from '@modules/auth/jwt.service';
import { WebsocketAuthMiddleware } from '@modules/auth/middleware/websocket.middleware';
import { ChatEvent } from '@modules/chat/events/chat.events';
import { PrismaService } from '@modules/prisma/prisma.service';
import { OnModuleInit } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import {
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({ namespace: 'chat', cors: { origin: '*' } })
export class ChatGateway implements OnModuleInit {
  constructor(
    private readonly jwtService: JwtService,
    private readonly authService: AuthService,
    private readonly prismaService: PrismaService,
  ) {}

  @WebSocketServer()
  server: Server;

  afterInit(client: Socket) {
    client.use(
      WebsocketAuthMiddleware(this.jwtService, this.authService) as any,
    );
  }

  async onModuleInit() {
    if (process.env.IS_CLI !== 'true') {
      this.initializeWebSocket();
    }
  }

  private initializeWebSocket() {
    if (this.server) {
      this.server.on('connection', (socket) => {
        socket.join((socket as any).user['userId']);
      });
    }
  }

  @OnEvent('chat.message')
  async message(event: ChatEvent) {
    if (this.server) {
      event.recipients.forEach((recipient) =>
        this.server.in(recipient).emit('message', event.message),
      );
    }
  }

  // Añadir método para notificar a doctores sobre mensajes de admin
  @SubscribeMessage('admin_message')
  async handleAdminMessage(
    @MessageBody() data: { conversationId: string; doctorId: string },
  ) {
    try {
      // Obtener información del doctor
      const doctor = await this.prismaService.doctor.findFirst({
        where: { userId: data.doctorId },
        include: { user: true },
      });

      if (!doctor) {
        return;
      }

      // Obtener información de la conversación
      const conversation = await this.prismaService.conversation.findUnique({
        where: { id: data.conversationId },
        include: {
          patient: {
            include: { user: true },
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      if (!conversation || conversation.type !== 'doctorAdmin') {
        return;
      }

      // Enviar notificación al doctor
      this.server.to(`user_${data.doctorId}`).emit('admin_notification', {
        conversationId: data.conversationId,
        patientId: conversation.patientId,
        patientName: `${conversation.patient.user.firstName} ${conversation.patient.user.lastName}`,
        message: conversation.messages[0]?.content || 'New message from admin',
      });
    } catch (error) {
      console.error('Error sending admin notification:', error);
    }
  }
}
