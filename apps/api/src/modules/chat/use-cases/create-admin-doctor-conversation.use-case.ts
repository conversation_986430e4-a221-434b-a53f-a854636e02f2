import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateAdminDoctorConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async execute({
    doctorUserId,
    patientId,
    adminUserId,
  }: {
    doctorUserId: string;
    patientId: string;
    adminUserId?: string; // Opcional, si se inicia desde el admin
  }) {
    // Obtener el userId del paciente
    const patient = await this.prismaService.patient.findUnique({
      where: { id: patientId },
      select: { userId: true },
    });

    if (!patient) {
      throw new Error(`Patient with ID ${patientId} not found`);
    }

    // Verificar si ya existe una conversación admin-doctor para este paciente
    const existingConversation = await this.prismaService.conversation.findFirst({
      where: { 
        patientId,
        type: 'doctorAdmin',
      },
    });

    // Si existe, devolver el ID
    if (existingConversation) {
      // Si hay un adminUserId y la conversación no está asignada, asignarla
      if (adminUserId && !existingConversation.assignedAdminId) {
        await this.prismaService.conversation.update({
          where: { id: existingConversation.id },
          data: { 
            assignedAdminId: adminUserId,
            assignmentStatus: 'assigned',
          },
        });
      }
      
      return existingConversation.id;
    }

    // Crear nueva conversación
    return this.prismaService.$transaction(async (prisma) => {
      // Crear la conversación
      const conversation = await prisma.conversation.create({
        data: {
          userId: patient.userId, // Asociamos al usuario del paciente para mantener la estructura
          patientId,
          type: 'doctorAdmin',
          status: 'active',
          assignmentStatus: adminUserId ? 'assigned' : 'unassigned',
          assignedAdminId: adminUserId || null,
          isAdminClosed: false,
        },
      });

      // Crear watcher para el doctor
      await prisma.conversationWatcher.create({
        data: {
          userId: doctorUserId,
          conversationId: conversation.id,
          unreadMessages: 0,
        },
      });

      // Si hay un admin, crear watcher para él también
      if (adminUserId) {
        await prisma.conversationWatcher.create({
          data: {
            userId: adminUserId,
            conversationId: conversation.id,
            unreadMessages: 0,
          },
        });
      }

      return conversation.id;
    });
  }
}