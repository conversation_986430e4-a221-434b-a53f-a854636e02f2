import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ManageAdminDoctorConversationUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async assignToAdmin(conversationId: string, adminUserId: string) {
    // Verificar que la conversación sea de tipo doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: { 
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Asignar la conversación al admin
    await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: { 
        assignedAdminId: adminUserId,
        assignmentStatus: 'assigned',
      },
    });

    // Verificar si el admin ya tiene un watcher para esta conversación
    const existingWatcher = await this.prismaService.conversationWatcher.findFirst({
      where: {
        conversationId,
        userId: adminUserId,
      },
    });

    // Si no existe, crear uno
    if (!existingWatcher) {
      await this.prismaService.conversationWatcher.create({
        data: {
          userId: adminUserId,
          conversationId,
          unreadMessages: 0,
        },
      });
    }

    return { success: true };
  }

  async unassignAdmin(conversationId: string) {
    // Verificar que la conversación sea de tipo doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: { 
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Desasignar la conversación
    await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: { 
        assignedAdminId: null,
        assignmentStatus: 'unassigned',
      },
    });

    return { success: true };
  }

  async closeConversation(conversationId: string) {
    // Verificar que la conversación sea de tipo doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: { 
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Cerrar la conversación y desasignar
    await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: { 
        isAdminClosed: true,
        assignedAdminId: null,
        assignmentStatus: 'unassigned',
      },
    });

    return { success: true };
  }

  async reopenConversation(conversationId: string) {
    // Verificar que la conversación sea de tipo doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: { 
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Reabrir la conversación
    await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: { 
        isAdminClosed: false,
      },
    });

    return { success: true };
  }
}